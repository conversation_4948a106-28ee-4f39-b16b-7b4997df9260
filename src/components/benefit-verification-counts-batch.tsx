'use client'

import { BenefitVerificationCounts } from '@/components/benefit-verification-counts'
import { BenefitVerificationCountsOptimized } from '@/components/benefit-verification-counts-optimized'

interface BenefitVerificationCountsBatchProps {
  companyBenefitId: string
  className?: string
  showDetails?: boolean
  isVerified?: boolean
  // For optimized version
  verificationCounts?: {
    confirmed: number
    disputed: number
    total: number
  } | null
  isLoading?: boolean
  useOptimized?: boolean
  // Batch verification status (passed from parent)
  userVerificationStatus?: {
    hasVerified: boolean
    isAdminVerified: boolean
  }
}

export function BenefitVerificationCountsBatch({
  companyBenefitId,
  className = '',
  showDetails = true,
  isVerified = false,
  verificationCounts,
  isLoading = false,
  useOptimized = false,
  userVerificationStatus
}: BenefitVerificationCountsBatchProps) {
  if (useOptimized) {
    return (
      <BenefitVerificationCountsOptimized
        companyBenefitId={companyBenefitId}
        className={className}
        verificationCounts={verificationCounts}
        isLoading={isLoading}
        showDetails={showDetails}
        isVerified={isVerified}
        userVerificationStatus={userVerificationStatus}
      />
    )
  }

  return (
    <BenefitVerificationCounts
      companyBenefitId={companyBenefitId}
      className={className}
      showDetails={showDetails}
      isVerified={isVerified}
      userVerificationStatus={userVerificationStatus}
    />
  )
}
