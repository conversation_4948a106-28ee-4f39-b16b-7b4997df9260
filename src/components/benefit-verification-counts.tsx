'use client'

import { useState, useEffect, useCallback } from 'react'

interface BenefitVerificationCountsProps {
  companyBenefitId: string
  className?: string
  showDetails?: boolean // Whether to show counts and criteria explanation
  isVerified?: boolean // Whether the benefit is already verified (hides criteria text)
  userVerificationStatus?: {
    hasVerified: boolean
    isAdminVerified: boolean
  } // User's verification status for this benefit
}

interface VerificationCounts {
  confirmed: number
  disputed: number
  total: number
}

export function BenefitVerificationCounts({
  companyBenefitId,
  className = '',
  showDetails = true,
  isVerified = false,
  userVerificationStatus
}: BenefitVerificationCountsProps) {
  const [verificationCounts, setVerificationCounts] = useState<VerificationCounts>({ confirmed: 0, disputed: 0, total: 0 })
  const [isLoading, setIsLoading] = useState(true)

  const fetchVerificationCounts = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/benefit-verifications/${companyBenefitId}`)
      if (response.ok) {
        const data = await response.json()
        setVerificationCounts(data)
      }
    } catch (error) {
      console.error('Error fetching verification counts:', error)
    } finally {
      setIsLoading(false)
    }
  }, [companyBenefitId])

  useEffect(() => {
    fetchVerificationCounts()
  }, [companyBenefitId, fetchVerificationCounts])

  // Don't render anything if loading or details are hidden
  // But DO render for verified benefits even if no user verifications (for admin-verified status)
  if (isLoading || !showDetails) {
    return null
  }

  // Always render when verification counts are available to show status
  // This ensures consistent behavior with the optimized version

  return (
    <div className={`text-xs text-gray-600 mt-2 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-1">
          {verificationCounts.total > 0 ? (
            <>
              <span className="text-green-600 font-medium">{verificationCounts.confirmed} confirmed</span>
              {verificationCounts.disputed > 0 && (
                <>
                  <span>•</span>
                  <span className="text-red-600 font-medium">{verificationCounts.disputed} disputed</span>
                </>
              )}
            </>
          ) : isVerified ? (
            <span className="text-green-600 font-medium">No user confirmation needed</span>
          ) : (
            <span className="text-orange-600 font-medium">0 confirmations</span>
          )}
        </div>

        {/* Status message integrated on the same line */}
        {userVerificationStatus && (userVerificationStatus.hasVerified || userVerificationStatus.isAdminVerified) ? (
          <div className="flex items-center gap-1 text-green-700">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
            </svg>
            <span>
              {userVerificationStatus.isAdminVerified
                ? "Admin verified"
                : "You verified"
              }
            </span>
          </div>
        ) : isVerified ? (
          <div className="flex items-center gap-1 text-green-700">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
            </svg>
            <span>Admin verified</span>
          </div>
        ) : verificationCounts.total === 0 && !isVerified ? (
          <div className="flex items-center gap-1 text-orange-600">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
            </svg>
            <span>You have not verified</span>
          </div>
        ) : null}
      </div>

      {!isVerified && (
        <div className="text-xs text-gray-600 mt-1">
          Benefits need 2+ confirmations and more confirmations than disputes to be verified
        </div>
      )}
    </div>
  )
}
