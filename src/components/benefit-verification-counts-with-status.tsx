'use client'

import { BenefitVerificationCounts } from '@/components/benefit-verification-counts'
import { BenefitVerificationCountsOptimized } from '@/components/benefit-verification-counts-optimized'
import { useUserVerificationStatus } from '@/hooks/use-user-verification-status'

interface BenefitVerificationCountsWithStatusProps {
  companyBenefitId: string
  className?: string
  showDetails?: boolean
  isVerified?: boolean
  // For optimized version
  verificationCounts?: {
    confirmed: number
    disputed: number
    total: number
  } | null
  isLoading?: boolean
  useOptimized?: boolean
}

export function BenefitVerificationCountsWithStatus({
  companyBenefitId,
  className = '',
  showDetails = true,
  isVerified = false,
  verificationCounts,
  isLoading = false,
  useOptimized = false
}: BenefitVerificationCountsWithStatusProps) {
  // Get user verification status for this benefit
  const { hasVerified, isAdminVerified } = useUserVerificationStatus(companyBenefitId)

  const userVerificationStatus = {
    hasVerified,
    isAdminVerified
  }

  if (useOptimized) {
    return (
      <BenefitVerificationCountsOptimized
        companyBenefitId={companyBenefitId}
        className={className}
        verificationCounts={verificationCounts}
        isLoading={isLoading}
        showDetails={showDetails}
        isVerified={isVerified}
        userVerificationStatus={userVerificationStatus}
      />
    )
  }

  return (
    <BenefitVerificationCounts
      companyBenefitId={companyBenefitId}
      className={className}
      showDetails={showDetails}
      isVerified={isVerified}
      userVerificationStatus={userVerificationStatus}
    />
  )
}
