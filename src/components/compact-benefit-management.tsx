'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { CheckCircle, Plus, Trash2, AlertTriangle, Shield, Users, Clock, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { BenefitVerification } from '@/components/benefit-verification'
import { CompanyVerificationNotice } from '@/components/company-verification-notice'
import { BatchBenefitSelection } from '@/components/batch-benefit-selection'

import { useCompanyAuthorization } from '@/hooks/use-company-authorization'
import { useBatchBenefitVerifications } from '@/hooks/use-batch-benefit-verifications'
import { useBatchUserVerificationStatus } from '@/hooks/use-batch-user-verification-status'

interface CompanyBenefit {
  id: string
  benefit_id: string
  name: string
  category: string
  icon?: string
  is_verified: boolean
  is_admin_verified?: boolean
  added_by?: string
  created_at: string
}

interface CompactBenefitManagementProps {
  companyId: string
  companyName: string
  canManage: boolean
}

export function CompactBenefitManagement({ companyId, companyName, canManage }: CompactBenefitManagementProps) {
  const [companyBenefits, setCompanyBenefits] = useState<CompanyBenefit[]>([])
  const [loading, setLoading] = useState(false)
  const [showBatchModal, setShowBatchModal] = useState(false)
  const [showDisputeForm, setShowDisputeForm] = useState<string | null>(null)
  const [disputeReason, setDisputeReason] = useState('')
  const [isSubmittingDispute, setIsSubmittingDispute] = useState(false)
  const [expandedBenefit, setExpandedBenefit] = useState<string | null>(null)

  // Get all company benefit IDs for batch verification loading
  const companyBenefitIds = useMemo(() => companyBenefits.map(cb => cb.id), [companyBenefits])

  // Use company-level authorization
  const { authStatus, isLoading: isLoadingAuth } = useCompanyAuthorization(companyId)

  // Use batch benefit verifications for better performance
  const { getVerificationCounts, isLoading: isLoadingVerifications, refetch: refetchBatchVerificationCounts } = useBatchBenefitVerifications(companyBenefitIds)

  // Use batch user verification status for better performance
  const { getVerificationStatus, isLoading: isLoadingUserVerifications, refetch: refetchBatchUserVerifications } = useBatchUserVerificationStatus(companyBenefitIds)

  const fetchCompanyBenefits = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/companies/${companyId}/benefits`)
      if (response.ok) {
        const data = await response.json()
        setCompanyBenefits(data)
      }
    } catch (error) {
      console.error('Error fetching company benefits:', error)
    } finally {
      setLoading(false)
    }
  }, [companyId])

  useEffect(() => {
    fetchCompanyBenefits()
  }, [companyId, fetchCompanyBenefits])

  const handleBatchSuccess = () => {
    fetchCompanyBenefits()
    setShowBatchModal(false)
  }

  const handleRemoveBenefit = async (benefitId: string, benefitName: string, isVerified: boolean, companyBenefitId: string) => {
    if (!isVerified) {
      if (!confirm(`Remove "${benefitName}" from ${companyName}? This will also delete all verifications.`)) {
        return
      }

      try {
        const response = await fetch(`/api/companies/${companyId}/benefits?benefitId=${benefitId}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          alert('Benefit removed successfully!')
          fetchCompanyBenefits()
        } else {
          const error = await response.json()
          alert(error.error || 'Failed to remove benefit')
        }
      } catch (error) {
        console.error('Error removing benefit:', error)
        alert('Failed to remove benefit')
      }
    } else {
      setShowDisputeForm(companyBenefitId)
    }
  }

  const handleSubmitDispute = async (companyBenefitId: string, benefitName: string) => {
    if (!disputeReason.trim()) {
      alert('Please provide a reason for the dispute.')
      return
    }

    setIsSubmittingDispute(true)
    try {
      const response = await fetch('/api/benefit-removal-disputes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyBenefitId,
          reason: disputeReason.trim()
        })
      })

      if (response.ok) {
        alert(`Dispute submitted for "${benefitName}". An admin will review your request.`)
        setShowDisputeForm(null)
        setDisputeReason('')
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to submit dispute')
      }
    } catch (error) {
      console.error('Error submitting dispute:', error)
      alert('Failed to submit dispute')
    } finally {
      setIsSubmittingDispute(false)
    }
  }

  // Group benefits by category
  const categorizedBenefits = companyBenefits.reduce((acc, benefit) => {
    const category = benefit.category || 'other'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(benefit)
    return acc
  }, {} as Record<string, CompanyBenefit[]>)

  const categories = [
    { key: 'health', label: 'Health & Medical' },
    { key: 'time_off', label: 'Time Off' },
    { key: 'financial', label: 'Financial' },
    { key: 'development', label: 'Development' },
    { key: 'wellness', label: 'Wellness' },
    { key: 'work_life', label: 'Work-Life Balance' },
    { key: 'transportation', label: 'Transportation' },
    { key: 'other', label: 'Other' },
  ]

  const existingBenefitIds = companyBenefits.map(cb => cb.benefit_id)

  // Helper function to get benefit status
  const getBenefitStatus = (benefit: CompanyBenefit) => {
    const verificationCounts = getVerificationCounts(benefit.id)
    const userStatus = getVerificationStatus(benefit.id)
    
    if (benefit.is_verified) {
      if (verificationCounts && verificationCounts.total === 0) {
        return { type: 'admin-verified', label: 'Admin Verified', color: 'text-blue-600' }
      }
      return { type: 'verified', label: 'Verified', color: 'text-green-600' }
    }
    
    if (verificationCounts) {
      if (verificationCounts.disputed > verificationCounts.confirmed) {
        return { type: 'disputed', label: 'Disputed', color: 'text-red-600' }
      }
      if (verificationCounts.confirmed > 0) {
        return { type: 'pending', label: 'Pending Verification', color: 'text-orange-600' }
      }
    }
    
    return { type: 'unverified', label: 'Unverified', color: 'text-gray-600' }
  }

  // Helper function to get progress percentage
  const getProgressPercentage = (benefit: CompanyBenefit) => {
    if (benefit.is_verified) return 100
    
    const verificationCounts = getVerificationCounts(benefit.id)
    if (!verificationCounts || verificationCounts.total === 0) return 0
    
    const needed = 5 // Assuming 5 confirmations needed for verification
    return Math.min((verificationCounts.confirmed / needed) * 100, 100)
  }

  // Calculate summary stats
  const summaryStats = useMemo(() => {
    const total = companyBenefits.length
    const verified = companyBenefits.filter(b => b.is_verified).length
    const pending = companyBenefits.filter(b => {
      if (b.is_verified) return false
      const counts = getVerificationCounts(b.id)
      return counts && counts.confirmed > 0 && counts.confirmed >= counts.disputed
    }).length
    const disputed = companyBenefits.filter(b => {
      if (b.is_verified) return false
      const counts = getVerificationCounts(b.id)
      return counts && counts.disputed > counts.confirmed
    }).length

    return { total, verified, pending, disputed }
  }, [companyBenefits, getVerificationCounts])

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-gray-600 mt-2">Loading benefits...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="flex-1 min-w-0">
          <h2 className="text-xl font-semibold text-gray-900">Company Benefits</h2>
          <p className="text-sm text-gray-600 mt-1 break-words">
            {canManage
              ? `Manage and verify benefits for ${companyName}. You can add, remove, and verify benefits based on your company email domain.`
              : `View and verify benefits offered by ${companyName}. You can confirm or dispute benefits based on your experience.`
            }
          </p>
        </div>
        {canManage && companyBenefits.length > 0 && (
          <Button
            onClick={() => setShowBatchModal(true)}
            className="flex items-center gap-2 w-full sm:w-auto flex-shrink-0"
          >
            <Plus className="w-4 h-4" />
            Add Benefits
          </Button>
        )}
      </div>

      {companyBenefits.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No benefits have been added for this company yet.</p>
          {canManage && (
            <Button
              onClick={() => setShowBatchModal(true)}
              className="mt-4 flex items-center gap-2 mx-auto w-full sm:w-auto"
            >
              <Plus className="w-4 h-4" />
              Add Benefits
            </Button>
          )}
        </div>
      ) : (
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 text-center">
            <h3 className="text-xl font-bold mb-2">Company Benefits Overview</h3>
            <p className="text-blue-100">Manage and verify your workplace benefits</p>
          </div>

          {/* Summary Stats */}
          <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{summaryStats.total}</div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">Total Benefits</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{summaryStats.verified}</div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">Verified</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{summaryStats.pending}</div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">Pending</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{summaryStats.disputed}</div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">Disputed</div>
              </div>
            </div>
          </div>

          {/* Benefits List */}
          {categories.map((category) => {
            const categoryBenefits = categorizedBenefits[category.key] || []
            if (categoryBenefits.length === 0) return null

            return (
              <div key={category.key}>
                <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                  <h4 className="font-semibold text-gray-900 uppercase tracking-wide text-sm">
                    {category.label}
                  </h4>
                </div>
                
                {categoryBenefits.map((benefit, index) => {
                  const status = getBenefitStatus(benefit)
                  const verificationCounts = getVerificationCounts(benefit.id)
                  const userStatus = getVerificationStatus(benefit.id)
                  const progressPercentage = getProgressPercentage(benefit)
                  const isExpanded = expandedBenefit === benefit.id
                  
                  return (
                    <div key={benefit.id}>
                      <div 
                        className="flex items-center px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer"
                        onClick={() => setExpandedBenefit(isExpanded ? null : benefit.id)}
                      >
                        {/* Icon */}
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                          {benefit.icon ? (
                            <span className="text-lg">{benefit.icon}</span>
                          ) : (
                            <div className="w-6 h-6 bg-gray-300 rounded"></div>
                          )}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h5 className="font-semibold text-gray-900 truncate">{benefit.name}</h5>
                            <span className={`text-xs font-medium ${status.color}`}>
                              {status.label}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            {verificationCounts ? (
                              <>
                                <span className="flex items-center gap-1">
                                  <Users className="w-3 h-3" />
                                  {verificationCounts.confirmed} confirmations
                                </span>
                                {verificationCounts.disputed > 0 && (
                                  <span className="flex items-center gap-1 text-red-600">
                                    <AlertTriangle className="w-3 h-3" />
                                    {verificationCounts.disputed} disputes
                                  </span>
                                )}
                              </>
                            ) : benefit.is_verified ? (
                              <span className="flex items-center gap-1 text-blue-600">
                                <Shield className="w-3 h-3" />
                                Admin verified
                              </span>
                            ) : (
                              <span className="flex items-center gap-1 text-gray-500">
                                <Clock className="w-3 h-3" />
                                Awaiting verification
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Progress Indicator */}
                        <div className="w-16 h-1 bg-gray-200 rounded-full mr-4 overflow-hidden">
                          <div 
                            className={`h-full rounded-full transition-all duration-300 ${
                              status.type === 'verified' || status.type === 'admin-verified' ? 'bg-green-500' :
                              status.type === 'disputed' ? 'bg-red-500' :
                              status.type === 'pending' ? 'bg-orange-500' : 'bg-gray-300'
                            }`}
                            style={{ width: `${progressPercentage}%` }}
                          />
                        </div>

                        {/* Actions */}
                        <div className="flex items-center gap-2">
                          {!isLoadingAuth && authStatus && authStatus.authorized && (
                            <>
                              {userStatus?.hasVerified ? (
                                <span className="text-xs text-green-600 font-medium px-2 py-1 bg-green-50 rounded">
                                  ✓ Confirmed
                                </span>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-xs"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    setExpandedBenefit(benefit.id)
                                  }}
                                >
                                  Verify
                                </Button>
                              )}
                            </>
                          )}
                          
                          {canManage && (
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleRemoveBenefit(benefit.benefit_id, benefit.name, benefit.is_verified, benefit.id)
                              }}
                              className="p-1"
                              title={benefit.is_verified ? "Request removal of this benefit" : "Delete benefit"}
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Expanded Content */}
                      {isExpanded && (
                        <div className="px-6 py-4 bg-gray-50 border-b border-gray-100">
                          {/* Dispute Form */}
                          {showDisputeForm === benefit.id && (
                            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <h6 className="font-medium text-gray-900">
                                  Request Removal of "{benefit.name}"
                                </h6>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => setShowDisputeForm(null)}
                                  className="p-1"
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              </div>
                              <p className="text-sm text-gray-600 mb-3">
                                Please explain why this benefit should be removed from {companyName}:
                              </p>
                              <textarea
                                value={disputeReason}
                                onChange={(e) => setDisputeReason(e.target.value)}
                                placeholder="Explain why this benefit should be removed..."
                                className="w-full p-3 border border-gray-300 rounded-md text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                rows={3}
                              />
                              <div className="flex gap-2 mt-3">
                                <Button
                                  onClick={() => handleSubmitDispute(benefit.id, benefit.name)}
                                  disabled={isSubmittingDispute || !disputeReason.trim()}
                                  size="sm"
                                  className="bg-yellow-600 hover:bg-yellow-700"
                                >
                                  {isSubmittingDispute ? 'Submitting...' : 'Submit Dispute'}
                                </Button>
                                <Button
                                  onClick={() => setShowDisputeForm(null)}
                                  variant="outline"
                                  size="sm"
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          )}

                          {/* Benefit Verification Component */}
                          {!isLoadingAuth && authStatus && authStatus.authorized && (
                            <>
                              {(isLoadingVerifications || isLoadingUserVerifications) ? (
                                <div className="flex items-center space-x-2 text-sm text-gray-500 py-4">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
                                  <span>Loading verification data...</span>
                                </div>
                              ) : (
                                <BenefitVerification
                                  companyBenefitId={benefit.id}
                                  benefitName={benefit.name}
                                  companyName={companyName}
                                  companyAuthStatus={authStatus}
                                  hideAuthRestriction={true}
                                  userVerificationStatus={getVerificationStatus(benefit.id) || {
                                    hasVerified: false,
                                    isAdminVerified: false,
                                    verification: null
                                  }}
                                  onBatchRefetch={refetchBatchUserVerifications}
                                  onBatchVerificationCountsRefetch={refetchBatchVerificationCounts}
                                />
                              )}
                            </>
                          )}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            )
          })}

          {/* Quick Actions Footer */}
          <div className="bg-gray-50 px-6 py-4 flex justify-center gap-3">
            {canManage && (
              <Button
                onClick={() => setShowBatchModal(true)}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add New Benefits
              </Button>
            )}
            <Button variant="outline" className="flex items-center gap-2">
              Export Report
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              View Analytics
            </Button>
          </div>

          {/* Company-level verification notice */}
          {!isLoadingAuth && authStatus && !authStatus.authorized && (
            <div className="p-6 border-t border-gray-200">
              <CompanyVerificationNotice authStatus={authStatus} />
            </div>
          )}
        </div>
      )}

      {/* Batch Benefit Selection Modal */}
      <BatchBenefitSelection
        companyId={companyId}
        companyName={companyName}
        isOpen={showBatchModal}
        onClose={() => setShowBatchModal(false)}
        onSuccess={handleBatchSuccess}
        existingBenefitIds={existingBenefitIds}
      />
    </div>
  )
}
