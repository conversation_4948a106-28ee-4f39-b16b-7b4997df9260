import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { getCurrentUser } from '@/lib/auth'
import { withErrorHandling } from '@/lib/api-error-handler'

interface BatchUserVerificationStatus {
  [companyBenefitId: string]: {
    hasVerified: boolean
    isAdminVerified: boolean
    verification: {
      id: string
      status: string
      createdAt: string
    } | null
    message: string
  }
}

/**
 * POST /api/benefit-verifications/user/batch
 * Get user verification status for multiple company benefits at once
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  const { companyBenefitIds } = await request.json()

  if (!companyBenefitIds || !Array.isArray(companyBenefitIds) || companyBenefitIds.length === 0) {
    return NextResponse.json(
      { error: 'companyBenefitIds array is required' },
      { status: 400 }
    )
  }

  // Limit batch size to prevent abuse
  if (companyBenefitIds.length > 100) {
    return NextResponse.json(
      { error: 'Maximum 100 company benefit IDs allowed per batch' },
      { status: 400 }
    )
  }

  const user = await getCurrentUser()
  const result: BatchUserVerificationStatus = {}

  // Get all company benefits and their verification status
  const placeholders = companyBenefitIds.map((_, i) => `$${i + 1}`).join(',')
  const benefitsResult = await query(
    `SELECT id, is_verified FROM company_benefits WHERE id IN (${placeholders})`,
    companyBenefitIds
  )

  if (benefitsResult.rows.length === 0) {
    return NextResponse.json(result)
  }

  // Get user verifications for all benefits at once (if user is authenticated)
  let userVerifications: any[] = []
  if (user) {
    const userVerificationsResult = await query(
      `SELECT company_benefit_id, id, status, created_at 
       FROM benefit_verifications 
       WHERE company_benefit_id IN (${placeholders}) AND user_id = $${companyBenefitIds.length + 1}`,
      [...companyBenefitIds, user.id]
    )
    userVerifications = userVerificationsResult.rows
  }

  // Get verification counts for all benefits to determine admin verification
  const verificationCountsResult = await query(
    `SELECT 
       company_benefit_id,
       COUNT(*) as total,
       COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed,
       COUNT(CASE WHEN status = 'disputed' THEN 1 END) as disputed
     FROM benefit_verifications 
     WHERE company_benefit_id IN (${placeholders})
     GROUP BY company_benefit_id`,
    companyBenefitIds
  )

  // Create a map for quick lookup
  const verificationCountsMap = new Map()
  verificationCountsResult.rows.forEach(row => {
    verificationCountsMap.set(row.company_benefit_id, {
      total: parseInt(row.total),
      confirmed: parseInt(row.confirmed),
      disputed: parseInt(row.disputed)
    })
  })

  const userVerificationsMap = new Map()
  userVerifications.forEach(verification => {
    userVerificationsMap.set(verification.company_benefit_id, verification)
  })

  // Process each benefit
  for (const benefit of benefitsResult.rows) {
    const companyBenefitId = benefit.id
    const isVerified = benefit.is_verified
    const userVerification = userVerificationsMap.get(companyBenefitId)
    const hasUserVerified = !!userVerification
    const counts = verificationCountsMap.get(companyBenefitId) || { confirmed: 0, disputed: 0, total: 0 }

    // Determine if this is admin-verified by checking if it's verified but doesn't meet user criteria
    let isAdminVerified = false
    if (isVerified) {
      const meetsUserCriteria = counts.confirmed >= 2 && counts.confirmed > counts.disputed
      isAdminVerified = isVerified && !meetsUserCriteria
    }

    result[companyBenefitId] = {
      hasVerified: hasUserVerified || isAdminVerified,
      isAdminVerified,
      verification: userVerification ? {
        id: userVerification.id,
        status: userVerification.status,
        createdAt: userVerification.created_at
      } : null,
      message: isAdminVerified
        ? 'Benefit is admin-verified'
        : hasUserVerified
          ? 'User has verified this benefit'
          : 'User has not verified this benefit'
    }
  }

  return NextResponse.json(result)
})
