<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Benefit Status Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Benefit Status Display Test</h1>
        
        <div class="grid gap-4">
            <!-- Benefit with 0 confirmations, user not verified -->
            <div class="bg-white p-4 rounded-lg border">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-medium">🚲 Bike Leasing (Dienstradleasing)</h3>
                    <button class="text-red-600 hover:text-red-700">Delete benefit</button>
                </div>
                
                <!-- New status display for 0 confirmations -->
                <div class="text-xs text-gray-600 mt-2">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-1">
                            <span class="text-orange-600 font-medium">0 confirmations</span>
                        </div>
                        <div class="flex items-center gap-1 text-orange-600">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span>You have not verified</span>
                        </div>
                    </div>
                    <div class="text-xs text-gray-600 mt-1">
                        Benefits need 2+ confirmations and more confirmations than disputes to be verified
                    </div>
                </div>
            </div>

            <!-- Benefit with 1 confirmation, user verified -->
            <div class="bg-white p-4 rounded-lg border">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-medium">🏃‍♂️ Sports Programs (Sportprogramme)</h3>
                    <button class="text-red-600 hover:text-red-700">Delete benefit</button>
                </div>
                
                <!-- Status display for 1 confirmation -->
                <div class="text-xs text-gray-600 mt-2">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-1">
                            <span class="text-green-600 font-medium">1 confirmed</span>
                        </div>
                        <div class="flex items-center gap-1 text-green-700">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span>You verified</span>
                        </div>
                    </div>
                    <div class="text-xs text-gray-600 mt-1">
                        Benefits need 2+ confirmations and more confirmations than disputes to be verified
                    </div>
                </div>
            </div>

            <!-- Benefit with 0 confirmations, admin verified -->
            <div class="bg-white p-4 rounded-lg border">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-medium">🏃‍♂️ Flexible Working Hours (Gleitzeit)</h3>
                    <button class="text-yellow-600 hover:text-yellow-700">Request removal of this benefit</button>
                </div>

                <!-- Status display for admin verified with 0 confirmations -->
                <div class="text-xs text-gray-600 mt-2">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-1">
                            <span class="text-green-600 font-medium">No user confirmation needed</span>
                        </div>
                        <div class="flex items-center gap-1 text-green-700">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Admin verified</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Benefit with 2 confirmations, user verified -->
            <div class="bg-white p-4 rounded-lg border">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-medium">🦷 Dental Insurance Plus (Zahnzusatzversicherung)</h3>
                    <button class="text-yellow-600 hover:text-yellow-700">Request removal of this benefit</button>
                </div>

                <!-- Status display for 2 confirmations -->
                <div class="text-xs text-gray-600 mt-2">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-1">
                            <span class="text-green-600 font-medium">2 confirmed</span>
                        </div>
                        <div class="flex items-center gap-1 text-green-700">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span>You verified</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 p-4 bg-blue-50 rounded-lg">
            <h2 class="font-medium text-blue-900 mb-2">Status Legend:</h2>
            <ul class="text-sm text-blue-800 space-y-1">
                <li><span class="text-orange-600 font-medium">0 confirmations</span> + <span class="text-orange-600">You have not verified</span> = Benefit needs user verification</li>
                <li><span class="text-green-600 font-medium">No user confirmation needed</span> + <span class="text-green-700">Admin verified</span> = Admin-verified benefit</li>
                <li><span class="text-green-600 font-medium">1+ confirmed</span> + <span class="text-green-700">You verified</span> = You have verified this benefit</li>
                <li><span class="text-green-600 font-medium">2+ confirmed</span> = Benefit is verified by the community</li>
            </ul>
        </div>
    </div>
</body>
</html>
