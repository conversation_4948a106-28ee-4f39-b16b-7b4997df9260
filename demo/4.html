<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Information-Dense Cards</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; padding: 2rem; }
        
        .card-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(380px, 1fr)); gap: 1.5rem; max-width: 1400px; margin: 0 auto; }
        
        .benefit-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .benefit-card:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); transform: translateY(-2px); }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            position: relative;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .benefit-icon-header {
            width: 56px;
            height: 56px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            backdrop-filter: blur(10px);
        }
        
        .benefit-details h3 {
            font-size: 1.375rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .benefit-category {
            font-size: 0.875rem;
            opacity: 0.9;
            font-weight: 500;
        }
        
        .verification-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .metric-card {
            text-align: center;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }
        
        .metric-label {
            font-size: 0.75rem;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .verification-timeline {
            margin-bottom: 1.5rem;
        }
        
        .timeline-header {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .timeline-item:last-child { border-bottom: none; }
        
        .timeline-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            flex-shrink: 0;
        }
        
        .icon-confirmed { background: #dcfce7; color: #166534; }
        .icon-disputed { background: #fecaca; color: #991b1b; }
        .icon-added { background: #dbeafe; color: #1e40af; }
        
        .timeline-content {
            flex: 1;
            font-size: 0.875rem;
            color: #4b5563;
        }
        
        .timeline-date {
            font-size: 0.75rem;
            color: #9ca3af;
        }
        
        .action-section {
            border-top: 1px solid #f1f5f9;
            padding-top: 1rem;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 0.75rem;
        }
        
        .primary-action {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .action-confirm { background: #10b981; color: white; }
        .action-confirm:hover { background: #059669; }
        
        .action-verify { background: #3b82f6; color: white; }
        .action-verify:hover { background: #2563eb; }
        
        .secondary-action {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .secondary-action:hover { background: #f8fafc; }
        
        .additional-info {
            margin-top: 1rem;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 6px;
            font-size: 0.75rem;
            color: #64748b;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <!-- Verified Benefit -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="verification-badge">✓ Verified</div>
                <div class="header-content">
                    <div class="benefit-icon-header">🚴</div>
                    <div class="benefit-details">
                        <h3>Bike Leasing Program</h3>
                        <div class="benefit-category">Transportation • Financial</div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">12</div>
                        <div class="metric-label">Confirmations</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0</div>
                        <div class="metric-label">Disputes</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">95%</div>
                        <div class="metric-label">Confidence</div>
                    </div>
                </div>
                
                <div class="verification-timeline">
                    <div class="timeline-header">Recent Activity</div>
                    <div class="timeline-item">
                        <div class="timeline-icon icon-confirmed">✓</div>
                        <div class="timeline-content">Admin verified this benefit</div>
                        <div class="timeline-date">2 days ago</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-icon icon-confirmed">✓</div>
                        <div class="timeline-content">3 users confirmed availability</div>
                        <div class="timeline-date">1 week ago</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-icon icon-added">+</div>
                        <div class="timeline-content">Benefit added to company</div>
                        <div class="timeline-date">2 weeks ago</div>
                    </div>
                </div>
                
                <div class="action-section">
                    <div class="action-grid">
                        <button class="primary-action action-confirm">Confirm Benefit</button>
                        <button class="secondary-action">More Info</button>
                    </div>
                    <div class="additional-info">
                        This benefit has been verified by the community and company administrators. 
                        Your confirmation helps other employees discover available benefits.
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Verification -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="verification-badge">Pending</div>
                <div class="header-content">
                    <div class="benefit-icon-header">🏋️</div>
                    <div class="benefit-details">
                        <h3>Gym Membership</h3>
                        <div class="benefit-category">Health • Wellness</div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">2</div>
                        <div class="metric-label">Confirmations</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">3</div>
                        <div class="metric-label">Needed</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">40%</div>
                        <div class="metric-label">Progress</div>
                    </div>
                </div>
                
                <div class="verification-timeline">
                    <div class="timeline-header">Verification Progress</div>
                    <div class="timeline-item">
                        <div class="timeline-icon icon-confirmed">✓</div>
                        <div class="timeline-content">2 employees confirmed</div>
                        <div class="timeline-date">3 days ago</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-icon icon-added">+</div>
                        <div class="timeline-content">Benefit submitted for verification</div>
                        <div class="timeline-date">1 week ago</div>
                    </div>
                </div>
                
                <div class="action-section">
                    <div class="action-grid">
                        <button class="primary-action action-verify">Help Verify</button>
                        <button class="secondary-action">Details</button>
                    </div>
                    <div class="additional-info">
                        This benefit needs 3 more confirmations from employees to be verified. 
                        Help your colleagues by confirming if this benefit is available.
                    </div>
                </div>
            </div>
        </div>

        <!-- Disputed Benefit -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="verification-badge">⚠️ Disputed</div>
                <div class="header-content">
                    <div class="benefit-icon-header">🧘</div>
                    <div class="benefit-details">
                        <h3>Wellness Programs</h3>
                        <div class="benefit-category">Health • Mental Health</div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">1</div>
                        <div class="metric-label">Confirmations</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">2</div>
                        <div class="metric-label">Disputes</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">33%</div>
                        <div class="metric-label">Confidence</div>
                    </div>
                </div>
                
                <div class="verification-timeline">
                    <div class="timeline-header">Recent Disputes</div>
                    <div class="timeline-item">
                        <div class="timeline-icon icon-disputed">!</div>
                        <div class="timeline-content">User reported benefit unavailable</div>
                        <div class="timeline-date">1 day ago</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-icon icon-disputed">!</div>
                        <div class="timeline-content">Benefit availability questioned</div>
                        <div class="timeline-date">3 days ago</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-icon icon-confirmed">✓</div>
                        <div class="timeline-content">1 user confirmed</div>
                        <div class="timeline-date">1 week ago</div>
                    </div>
                </div>
                
                <div class="action-section">
                    <div class="action-grid">
                        <button class="primary-action action-verify">Resolve Dispute</button>
                        <button class="secondary-action">View All</button>
                    </div>
                    <div class="additional-info">
                        This benefit has conflicting reports. Please help resolve by confirming 
                        current availability or providing updated information.
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>