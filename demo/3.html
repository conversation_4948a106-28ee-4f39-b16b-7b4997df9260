<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Action-Oriented Cards</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; padding: 2rem; }
        
        .card-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 1.5rem; max-width: 1200px; margin: 0 auto; }
        
        .benefit-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }
        
        .benefit-card:hover { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); transform: translateY(-2px); }
        
        .card-header {
            padding: 1.5rem 1.5rem 1rem;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .benefit-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }
        
        .benefit-icon {
            width: 48px;
            height: 48px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .benefit-name {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
        }
        
        .benefit-description {
            font-size: 0.875rem;
            color: #64748b;
            line-height: 1.5;
        }
        
        .card-body {
            padding: 1rem 1.5rem;
        }
        
        .verification-summary {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .verification-count {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: #475569;
        }
        
        .count-number {
            font-weight: 700;
            color: #1e293b;
        }
        
        .verification-status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-verified { background: #dcfce7; color: #166534; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-action-needed { background: #fecaca; color: #991b1b; }
        
        .card-footer {
            padding: 1rem 1.5rem 1.5rem;
        }
        
        .primary-action {
            width: 100%;
            padding: 0.875rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .action-confirm { background: linear-gradient(135deg, #10b981, #059669); color: white; }
        .action-confirm:hover { background: linear-gradient(135deg, #059669, #047857); transform: translateY(-1px); }
        
        .action-verify { background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; }
        .action-verify:hover { background: linear-gradient(135deg, #2563eb, #1d4ed8); transform: translateY(-1px); }
        
        .action-dispute { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; }
        .action-dispute:hover { background: linear-gradient(135deg, #dc2626, #b91c1c); transform: translateY(-1px); }
        
        .secondary-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .secondary-btn {
            flex: 1;
            padding: 0.5rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .secondary-btn:hover { background: #f8fafc; border-color: #cbd5e1; }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            transition: width 0.3s ease;
        }
        
        .progress-text {
            font-size: 0.75rem;
            color: #64748b;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <!-- Verified Benefit -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="benefit-title">
                    <div class="benefit-icon">🚴</div>
                    <div>
                        <div class="benefit-name">Bike Leasing</div>
                    </div>
                </div>
                <div class="benefit-description">Company-sponsored bicycle leasing program for commuting</div>
            </div>
            <div class="card-body">
                <div class="verification-summary">
                    <div class="verification-count">
                        <span>👥</span>
                        <span><span class="count-number">12</span> confirmations</span>
                    </div>
                    <div class="verification-status status-verified">✓ Verified</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%;"></div>
                </div>
                <div class="progress-text">Fully verified by community</div>
            </div>
            <div class="card-footer">
                <button class="primary-action action-confirm">✓ Confirmed</button>
                <div class="secondary-actions">
                    <button class="secondary-btn">View Details</button>
                    <button class="secondary-btn">Report Issue</button>
                </div>
            </div>
        </div>

        <!-- Needs Verification -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="benefit-title">
                    <div class="benefit-icon">🏋️</div>
                    <div>
                        <div class="benefit-name">Gym Membership</div>
                    </div>
                </div>
                <div class="benefit-description">Fitness studio membership with multiple locations</div>
            </div>
            <div class="card-body">
                <div class="verification-summary">
                    <div class="verification-count">
                        <span>⏳</span>
                        <span><span class="count-number">2/5</span> confirmations</span>
                    </div>
                    <div class="verification-status status-pending">Needs Verification</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 40%;"></div>
                </div>
                <div class="progress-text">3 more confirmations needed</div>
            </div>
            <div class="card-footer">
                <button class="primary-action action-verify">Help Verify This Benefit</button>
                <div class="secondary-actions">
                    <button class="secondary-btn">Learn More</button>
                    <button class="secondary-btn">Skip</button>
                </div>
            </div>
        </div>

        <!-- Action Needed -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="benefit-title">
                    <div class="benefit-icon">🧘</div>
                    <div>
                        <div class="benefit-name">Wellness Programs</div>
                    </div>
                </div>
                <div class="benefit-description">Mental health and wellness support programs</div>
            </div>
            <div class="card-body">
                <div class="verification-summary">
                    <div class="verification-count">
                        <span>⚠️</span>
                        <span><span class="count-number">1</span> confirmation, <span class="count-number">2</span> disputes</span>
                    </div>
                    <div class="verification-status status-action-needed">Action Needed</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 20%; background: linear-gradient(90deg, #ef4444, #dc2626);"></div>
                </div>
                <div class="progress-text">Community feedback needed</div>
            </div>
            <div class="card-footer">
                <button class="primary-action action-dispute">Resolve Disputes</button>
                <div class="secondary-actions">
                    <button class="secondary-btn">View Feedback</button>
                    <button class="secondary-btn">Remove</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>