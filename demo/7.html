2<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data-Driven Analytics Cards</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; padding: 2rem; }
        
        .card-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 1.5rem; max-width: 1400px; margin: 0 auto; }
        
        .benefit-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .benefit-card:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); transform: translateY(-2px); }
        
        .card-header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: 1.5rem;
            position: relative;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .benefit-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .benefit-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            backdrop-filter: blur(10px);
        }
        
        .benefit-name {
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .trend-indicator {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }
        .trend-stable { color: #f59e0b; }
        
        .header-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }
        
        .header-metric {
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .metric-label {
            font-size: 0.75rem;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .analytics-section {
            margin-bottom: 1.5rem;
        }
        
        .section-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .chart-container {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .mini-chart {
            display: flex;
            align-items: end;
            gap: 2px;
            height: 60px;
            margin-bottom: 0.5rem;
        }
        
        .chart-bar {
            flex: 1;
            background: linear-gradient(to top, #3b82f6, #60a5fa);
            border-radius: 2px 2px 0 0;
            min-height: 4px;
            transition: all 0.2s ease;
        }
        
        .chart-bar:hover { background: linear-gradient(to top, #2563eb, #3b82f6); }
        
        .chart-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.625rem;
            color: #64748b;
        }
        
        .verification-breakdown {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .breakdown-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            border: 1px solid #e2e8f0;
        }
        
        .breakdown-value {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .breakdown-label {
            font-size: 0.75rem;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .breakdown-confirmed .breakdown-value { color: #10b981; }
        .breakdown-disputed .breakdown-value { color: #ef4444; }
        .breakdown-pending .breakdown-value { color: #f59e0b; }
        .breakdown-total .breakdown-value { color: #3b82f6; }
        
        .confidence-meter {
            margin-bottom: 1.5rem;
        }
        
        .confidence-bar {
            width: 100%;
            height: 12px;
            background: #e2e8f0;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }
        
        .confidence-fill {
            height: 100%;
            border-radius: 6px;
            transition: width 0.3s ease;
        }
        
        .confidence-high { background: linear-gradient(90deg, #10b981, #059669); }
        .confidence-medium { background: linear-gradient(90deg, #f59e0b, #d97706); }
        .confidence-low { background: linear-gradient(90deg, #ef4444, #dc2626); }
        
        .confidence-text {
            display: flex;
            justify-content: space-between;
            font-size: 0.75rem;
            color: #64748b;
        }
        
        .insights-list {
            list-style: none;
            margin-bottom: 1.5rem;
        }
        
        .insight-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0;
            font-size: 0.875rem;
            color: #4b5563;
        }
        
        .insight-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.625rem;
            flex-shrink: 0;
        }
        
        .insight-positive { background: #dcfce7; color: #166534; }
        .insight-warning { background: #fef3c7; color: #92400e; }
        .insight-info { background: #dbeafe; color: #1e40af; }
        
        .action-buttons {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 0.75rem;
        }
        
        .btn {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }
        
        .btn-primary { background: #3b82f6; color: white; }
        .btn-primary:hover { background: #2563eb; }
        
        .btn-secondary { background: #f1f5f9; color: #475569; border: 1px solid #e2e8f0; }
        .btn-secondary:hover { background: #e2e8f0; }
    </style>
</head>
<body>
    <div class="card-container">
        <!-- High Confidence Benefit -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="header-top">
                    <div class="benefit-info">
                        <div class="benefit-icon">🚴</div>
                        <div class="benefit-name">Bike Leasing Program</div>
                    </div>
                    <div class="trend-indicator trend-up">
                        <span>↗</span>
                        <span>+15% this month</span>
                    </div>
                </div>
                <div class="header-metrics">
                    <div class="header-metric">
                        <div class="metric-value">94%</div>
                        <div class="metric-label">Confidence</div>
                    </div>
                    <div class="header-metric">
                        <div class="metric-value">12</div>
                        <div class="metric-label">Verifications</div>
                    </div>
                    <div class="header-metric">
                        <div class="metric-value">4.8</div>
                        <div class="metric-label">Rating</div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="analytics-section">
                    <div class="section-title">
                        <span>📊</span>
                        <span>Verification Trend (Last 30 Days)</span>
                    </div>
                    <div class="chart-container">
                        <div class="mini-chart">
                            <div class="chart-bar" style="height: 20%;"></div>
                            <div class="chart-bar" style="height: 35%;"></div>
                            <div class="chart-bar" style="height: 45%;"></div>
                            <div class="chart-bar" style="height: 60%;"></div>
                            <div class="chart-bar" style="height: 80%;"></div>
                            <div class="chart-bar" style="height: 100%;"></div>
                            <div class="chart-bar" style="height: 90%;"></div>
                        </div>
                        <div class="chart-labels">
                            <span>Week 1</span>
                            <span>Week 2</span>
                            <span>Week 3</span>
                            <span>Week 4</span>
                        </div>
                    </div>
                </div>
                
                <div class="verification-breakdown">
                    <div class="breakdown-item breakdown-confirmed">
                        <div class="breakdown-value">12</div>
                        <div class="breakdown-label">Confirmed</div>
                    </div>
                    <div class="breakdown-item breakdown-disputed">
                        <div class="breakdown-value">0</div>
                        <div class="breakdown-label">Disputed</div>
                    </div>
                </div>
                
                <div class="confidence-meter">
                    <div class="section-title">
                        <span>🎯</span>
                        <span>Confidence Score</span>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill confidence-high" style="width: 94%;"></div>
                    </div>
                    <div class="confidence-text">
                        <span>Very High Confidence</span>
                        <span>94%</span>
                    </div>
                </div>
                
                <ul class="insights-list">
                    <li class="insight-item">
                        <div class="insight-icon insight-positive">✓</div>
                        <span>Consistently verified by employees</span>
                    </li>
                    <li class="insight-item">
                        <div class="insight-icon insight-positive">↗</div>
                        <span>Usage trending upward</span>
                    </li>
                    <li class="insight-item">
                        <div class="insight-icon insight-info">i</div>
                        <span>Popular among engineering team</span>
                    </li>
                </ul>
                
                <div class="action-buttons">
                    <button class="btn btn-primary">Confirm & Rate</button>
                    <button class="btn btn-secondary">Analytics</button>
                </div>
            </div>
        </div>

        <!-- Medium Confidence - Needs Data -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="header-top">
                    <div class="benefit-info">
                        <div class="benefit-icon">🏋️</div>
                        <div class="benefit-name">Gym Membership</div>
                    </div>
                    <div class="trend-indicator trend-stable">
                        <span>→</span>
                        <span>Stable</span>
                    </div>
                </div>
                <div class="header-metrics">
                    <div class="header-metric">
                        <div class="metric-value">67%</div>
                        <div class="metric-label">Confidence</div>
                    </div>
                    <div class="header-metric">
                        <div class="metric-value">4</div>
                        <div class="metric-label">Verifications</div>
                    </div>
                    <div class="header-metric">
                        <div class="metric-value">3.2</div>
                        <div class="metric-label">Rating</div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="analytics-section">
                    <div class="section-title">
                        <span>📊</span>
                        <span>Verification Trend (Last 30 Days)</span>
                    </div>
                    <div class="chart-container">
                        <div class="mini-chart">
                            <div class="chart-bar" style="height: 30%;"></div>
                            <div class="chart-bar" style="height: 25%;"></div>
                            <div class="chart-bar" style="height: 40%;"></div>
                            <div class="chart-bar" style="height: 35%;"></div>
                            <div class="chart-bar" style="height: 45%;"></div>
                            <div class="chart-bar" style="height: 50%;"></div>
                            <div class="chart-bar" style="height: 48%;"></div>
                        </div>
                        <div class="chart-labels">
                            <span>Week 1</span>
                            <span>Week 2</span>
                            <span>Week 3</span>
                            <span>Week 4</span>
                        </div>
                    </div>
                </div>
                
                <div class="verification-breakdown">
                    <div class="breakdown-item breakdown-confirmed">
                        <div class="breakdown-value">4</div>
                        <div class="breakdown-label">Confirmed</div>
                    </div>
                    <div class="breakdown-item breakdown-pending">
                        <div class="breakdown-value">2</div>
                        <div class="breakdown-label">Pending</div>
                    </div>
                </div>
                
                <div class="confidence-meter">
                    <div class="section-title">
                        <span>🎯</span>
                        <span>Confidence Score</span>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill confidence-medium" style="width: 67%;"></div>
                    </div>
                    <div class="confidence-text">
                        <span>Medium Confidence</span>
                        <span>67%</span>
                    </div>
                </div>
                
                <ul class="insights-list">
                    <li class="insight-item">
                        <div class="insight-icon insight-warning">!</div>
                        <span>Needs more verification data</span>
                    </li>
                    <li class="insight-item">
                        <div class="insight-icon insight-info">i</div>
                        <span>Mixed reviews on accessibility</span>
                    </li>
                    <li class="insight-item">
                        <div class="insight-icon insight-warning">?</div>
                        <span>Location details unclear</span>
                    </li>
                </ul>
                
                <div class="action-buttons">
                    <button class="btn btn-primary">Help Verify</button>
                    <button class="btn btn-secondary">Details</button>
                </div>
            </div>
        </div>

        <!-- Low Confidence - Disputed -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="header-top">
                    <div class="benefit-info">
                        <div class="benefit-icon">🧘</div>
                        <div class="benefit-name">Wellness Programs</div>
                    </div>
                    <div class="trend-indicator trend-down">
                        <span>↘</span>
                        <span>-20% this month</span>
                    </div>
                </div>
                <div class="header-metrics">
                    <div class="header-metric">
                        <div class="metric-value">34%</div>
                        <div class="metric-label">Confidence</div>
                    </div>
                    <div class="header-metric">
                        <div class="metric-value">3</div>
                        <div class="metric-label">Verifications</div>
                    </div>
                    <div class="header-metric">
                        <div class="metric-value">2.1</div>
                        <div class="metric-label">Rating</div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="analytics-section">
                    <div class="section-title">
                        <span>📊</span>
                        <span>Verification Trend (Last 30 Days)</span>
                    </div>
                    <div class="chart-container">
                        <div class="mini-chart">
                            <div class="chart-bar" style="height: 60%; background: linear-gradient(to top, #ef4444, #f87171);"></div>
                            <div class="chart-bar" style="height: 40%; background: linear-gradient(to top, #ef4444, #f87171);"></div>
                            <div class="chart-bar" style="height: 30%; background: linear-gradient(to top, #ef4444, #f87171);"></div>
                            <div class="chart-bar" style="height: 25%; background: linear-gradient(to top, #ef4444, #f87171);"></div>
                            <div class="chart-bar" style="height: 20%; background: linear-gradient(to top, #ef4444, #f87171);"></div>
                            <div class="chart-bar" style="height: 15%; background: linear-gradient(to top, #ef4444, #f87171);"></div>
                            <div class="chart-bar" style="height: 10%; background: linear-gradient(to top, #ef4444, #f87171);"></div>
                        </div>
                        <div class="chart-labels">
                            <span>Week 1</span>
                            <span>Week 2</span>
                            <span>Week 3</span>
                            <span>Week 4</span>
                        </div>
                    </div>
                </div>
                
                <div class="verification-breakdown">
                    <div class="breakdown-item breakdown-confirmed">
                        <div class="breakdown-value">1</div>
                        <div class="breakdown-label">Confirmed</div>
                    </div>
                    <div class="breakdown-item breakdown-disputed">
                        <div class="breakdown-value">2</div>
                        <div class="breakdown-label">Disputed</div>
                    </div>
                </div>
                
                <div class="confidence-meter">
                    <div class="section-title">
                        <span>🎯</span>
                        <span>Confidence Score</span>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill confidence-low" style="width: 34%;"></div>
                    </div>
                    <div class="confidence-text">
                        <span>Low Confidence</span>
                        <span>34%</span>
                    </div>
                </div>
                
                <ul class="insights-list">
                    <li class="insight-item">
                        <div class="insight-icon insight-warning">⚠</div>
                        <span>More disputes than confirmations</span>
                    </li>
                    <li class="insight-item">
                        <div class="insight-icon insight-warning">↘</div>
                        <span>Declining verification trend</span>
                    </li>
                    <li class="insight-item">
                        <div class="insight-icon insight-warning">!</div>
                        <span>May need admin review</span>
                    </li>
                </ul>
                
                <div class="action-buttons">
                    <button class="btn btn-primary">Resolve Issues</button>
                    <button class="btn btn-secondary">Report</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>