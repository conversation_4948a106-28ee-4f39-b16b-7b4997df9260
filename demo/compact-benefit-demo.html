<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BenefitLens - Compact List Design Implementation</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: #f8fafc; 
            padding: 2rem; 
            line-height: 1.6;
        }
        
        .demo-container { max-width: 900px; margin: 0 auto; }
        
        .benefits-list {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .list-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .list-subtitle {
            font-size: 0.875rem;
            opacity: 0.9;
        }
        
        .summary-stats {
            background: #f8fafc;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .summary-label {
            font-size: 0.75rem;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .category-divider {
            background: #f8fafc;
            padding: 0.75rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .benefit-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .benefit-item:last-child { border-bottom: none; }
        .benefit-item:hover { background: #f8fafc; }
        
        .benefit-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .benefit-content {
            flex: 1;
            min-width: 0;
        }
        
        .benefit-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }
        
        .benefit-name {
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        .status-badge {
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            font-size: 0.625rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .badge-verified { background: #dcfce7; color: #166534; }
        .badge-admin-verified { background: #dbeafe; color: #1e40af; }
        .badge-pending { background: #fef3c7; color: #92400e; }
        .badge-disputed { background: #fecaca; color: #991b1b; }
        .badge-unverified { background: #f1f5f9; color: #475569; }
        
        .benefit-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.75rem;
            color: #64748b;
        }
        
        .stat {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .progress-indicator {
            width: 60px;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
            margin-left: 1rem;
        }
        
        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .progress-verified { background: #10b981; }
        .progress-admin { background: #3b82f6; }
        .progress-pending { background: #f59e0b; }
        .progress-disputed { background: #ef4444; }
        .progress-unverified { background: #9ca3af; }
        
        .benefit-actions {
            display: flex;
            gap: 0.5rem;
            margin-left: 1rem;
        }
        
        .action-btn {
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-verify { background: #3b82f6; color: white; }
        .btn-verify:hover { background: #2563eb; }
        
        .btn-confirmed { background: #dcfce7; color: #166534; border: 1px solid #bbf7d0; }
        
        .btn-resolve { background: #f59e0b; color: white; }
        .btn-resolve:hover { background: #d97706; }
        
        .btn-remove { background: #ef4444; color: white; padding: 0.25rem; }
        .btn-remove:hover { background: #dc2626; }
        
        .btn-neutral { background: #f1f5f9; color: #475569; border: 1px solid #e2e8f0; }
        .btn-neutral:hover { background: #e2e8f0; }
        
        .quick-actions {
            padding: 1rem 1.5rem;
            background: #f8fafc;
            display: flex;
            gap: 0.75rem;
            justify-content: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .quick-btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .quick-primary { background: #3b82f6; color: white; }
        .quick-primary:hover { background: #2563eb; }
        
        .quick-secondary { background: white; color: #475569; border: 1px solid #e2e8f0; }
        .quick-secondary:hover { background: #f8fafc; }
        
        .icon { width: 12px; height: 12px; display: inline-block; }
        
        .demo-note {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            color: #1e40af;
        }
        
        .demo-note h3 {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .scenario-list {
            font-size: 0.875rem;
            line-height: 1.6;
        }
        
        .scenario-list li {
            margin-bottom: 0.25rem;
        }
        
        @media (max-width: 768px) {
            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }
            
            .benefit-stats {
                flex-direction: column;
                gap: 0.25rem;
            }
            
            .benefit-actions {
                flex-direction: column;
                gap: 0.25rem;
            }
            
            .quick-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-note">
            <h3>🎯 BenefitLens Compact List Design - All Scenarios Covered</h3>
            <p>This implementation shows how Design 8 would look in the actual BenefitLens app, covering all possible benefit states:</p>
            <ul class="scenario-list">
                <li><strong>Admin Verified:</strong> Benefits verified by administrators (blue badge)</li>
                <li><strong>Community Verified:</strong> Benefits verified by user consensus (green badge)</li>
                <li><strong>Pending Verification:</strong> Benefits with some confirmations but need more (orange badge)</li>
                <li><strong>Disputed:</strong> Benefits with more disputes than confirmations (red badge)</li>
                <li><strong>Unverified:</strong> New benefits with no verifications yet (gray badge)</li>
                <li><strong>User Actions:</strong> Different actions based on user's verification status</li>
                <li><strong>Management Controls:</strong> Remove/dispute actions for company managers</li>
            </ul>
        </div>

        <div class="benefits-list">
            <div class="list-header">
                <div class="list-title">TechCorp Benefits</div>
                <div class="list-subtitle">Manage and verify your workplace benefits</div>
            </div>
            
            <div class="summary-stats">
                <div class="summary-item">
                    <div class="summary-value" style="color: #1e293b;">12</div>
                    <div class="summary-label">Total Benefits</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" style="color: #10b981;">8</div>
                    <div class="summary-label">Verified</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" style="color: #f59e0b;">2</div>
                    <div class="summary-label">Pending</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" style="color: #ef4444;">2</div>
                    <div class="summary-label">Disputed</div>
                </div>
            </div>
            
            <!-- Health & Medical Category -->
            <div class="category-divider">Health & Medical</div>
            
            <!-- Admin Verified Benefit -->
            <div class="benefit-item">
                <div class="benefit-icon">🏥</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Health Insurance</div>
                        <div class="status-badge badge-admin-verified">Admin Verified</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">🛡️</span>
                            <span>Admin verified</span>
                        </div>
                        <div class="stat">
                            <span class="icon">📋</span>
                            <span>No user confirmation needed</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-admin" style="width: 100%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-confirmed">✓ Admin Verified</button>
                    <button class="action-btn btn-neutral">Info</button>
                </div>
            </div>
            
            <!-- Community Verified Benefit -->
            <div class="benefit-item">
                <div class="benefit-icon">🏋️</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Gym Membership</div>
                        <div class="status-badge badge-verified">Verified</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">👥</span>
                            <span>15 confirmations</span>
                        </div>
                        <div class="stat">
                            <span class="icon">⭐</span>
                            <span>4.8 rating</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-verified" style="width: 100%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-confirmed">✓ You Confirmed</button>
                    <button class="action-btn btn-neutral">Rate</button>
                </div>
            </div>
            
            <!-- Disputed Benefit -->
            <div class="benefit-item">
                <div class="benefit-icon">🧘</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Wellness Programs</div>
                        <div class="status-badge badge-disputed">Disputed</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">👥</span>
                            <span>2 confirmations</span>
                        </div>
                        <div class="stat">
                            <span class="icon">⚠️</span>
                            <span>5 disputes</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-disputed" style="width: 28%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-resolve">Resolve</button>
                    <button class="action-btn btn-neutral">Details</button>
                </div>
            </div>
            
            <!-- Transportation Category -->
            <div class="category-divider">Transportation</div>
            
            <!-- Highly Verified Benefit -->
            <div class="benefit-item">
                <div class="benefit-icon">🚴</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Bike Leasing Program</div>
                        <div class="status-badge badge-verified">Verified</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">👥</span>
                            <span>24 confirmations</span>
                        </div>
                        <div class="stat">
                            <span class="icon">📈</span>
                            <span>Trending up</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-verified" style="width: 100%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-verify">Verify</button>
                    <button class="action-btn btn-neutral">Share</button>
                </div>
            </div>
            
            <!-- Pending Verification -->
            <div class="benefit-item">
                <div class="benefit-icon">🚗</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Parking Allowance</div>
                        <div class="status-badge badge-pending">Pending</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">👥</span>
                            <span>3 confirmations</span>
                        </div>
                        <div class="stat">
                            <span class="icon">🎯</span>
                            <span>2 more needed</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-pending" style="width: 60%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-verify">Help Verify</button>
                    <button class="action-btn btn-neutral">Skip</button>
                </div>
            </div>
            
            <!-- Financial Category -->
            <div class="category-divider">Financial</div>
            
            <!-- Popular Verified Benefit -->
            <div class="benefit-item">
                <div class="benefit-icon">💰</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Performance Bonus</div>
                        <div class="status-badge badge-verified">Verified</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">👥</span>
                            <span>31 confirmations</span>
                        </div>
                        <div class="stat">
                            <span class="icon">⭐</span>
                            <span>4.9 rating</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-verified" style="width: 100%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-confirmed">✓ Confirmed</button>
                    <button class="action-btn btn-remove">🗑️</button>
                </div>
            </div>
            
            <!-- Unverified New Benefit -->
            <div class="benefit-item">
                <div class="benefit-icon">📈</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Stock Options</div>
                        <div class="status-badge badge-unverified">Unverified</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">⏰</span>
                            <span>Awaiting verification</span>
                        </div>
                        <div class="stat">
                            <span class="icon">🆕</span>
                            <span>Added 2 days ago</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-unverified" style="width: 0%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-verify">Be First to Verify</button>
                    <button class="action-btn btn-remove">🗑️</button>
                </div>
            </div>
            
            <!-- Development Category -->
            <div class="category-divider">Development</div>
            
            <!-- Mixed Feedback Benefit -->
            <div class="benefit-item">
                <div class="benefit-icon">📚</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Learning Budget</div>
                        <div class="status-badge badge-pending">Pending</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">👥</span>
                            <span>4 confirmations</span>
                        </div>
                        <div class="stat">
                            <span class="icon">⚠️</span>
                            <span>1 dispute</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-pending" style="width: 80%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-verify">Verify</button>
                    <button class="action-btn btn-neutral">Details</button>
                </div>
            </div>
            
            <!-- Work-Life Balance Category -->
            <div class="category-divider">Work-Life Balance</div>
            
            <!-- Controversial Benefit -->
            <div class="benefit-item">
                <div class="benefit-icon">🏠</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Remote Work Policy</div>
                        <div class="status-badge badge-disputed">Disputed</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">👥</span>
                            <span>8 confirmations</span>
                        </div>
                        <div class="stat">
                            <span class="icon">⚠️</span>
                            <span>12 disputes</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-disputed" style="width: 40%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-resolve">Resolve Dispute</button>
                    <button class="action-btn btn-neutral">View Feedback</button>
                </div>
            </div>
            
            <!-- Recently Added Benefit -->
            <div class="benefit-item">
                <div class="benefit-icon">⏰</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Flexible Hours</div>
                        <div class="status-badge badge-pending">Pending</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">👥</span>
                            <span>1 confirmation</span>
                        </div>
                        <div class="stat">
                            <span class="icon">🕐</span>
                            <span>Added 1 hour ago</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-pending" style="width: 20%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-verify">Verify</button>
                    <button class="action-btn btn-remove">🗑️</button>
                </div>
            </div>
            
            <!-- Other Category -->
            <div class="category-divider">Other</div>
            
            <!-- Company Perk -->
            <div class="benefit-item">
                <div class="benefit-icon">☕</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Free Coffee & Snacks</div>
                        <div class="status-badge badge-verified">Verified</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span class="icon">👥</span>
                            <span>42 confirmations</span>
                        </div>
                        <div class="stat">
                            <span class="icon">😊</span>
                            <span>Most popular</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-verified" style="width: 100%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-confirmed">✓ Love it!</button>
                    <button class="action-btn btn-neutral">Rate</button>
                </div>
            </div>
            
            <div class="quick-actions">
                <button class="quick-btn quick-primary">+ Add New Benefits</button>
                <button class="quick-btn quick-secondary">📊 Export Report</button>
                <button class="quick-btn quick-secondary">📈 View Analytics</button>
            </div>
        </div>
    </div>
</body>
</html>
