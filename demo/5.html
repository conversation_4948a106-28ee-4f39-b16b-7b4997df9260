<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile-First Responsive Cards</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: #f8fafc; 
            padding: 1rem; 
            line-height: 1.5;
        }
        
        .card-container { 
            display: grid; 
            grid-template-columns: 1fr;
            gap: 1rem; 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        
        @media (min-width: 640px) {
            .card-container { grid-template-columns: repeat(2, 1fr); gap: 1.5rem; }
            body { padding: 2rem; }
        }
        
        @media (min-width: 1024px) {
            .card-container { grid-template-columns: repeat(3, 1fr); }
        }
        
        .benefit-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
            transition: all 0.3s ease;
            touch-action: manipulation;
        }
        
        .benefit-card:hover { 
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); 
            transform: translateY(-2px); 
        }
        
        .card-header {
            padding: 1rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
        }
        
        @media (min-width: 640px) {
            .card-header { padding: 1.5rem; }
        }
        
        .benefit-main {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }
        
        .benefit-icon {
            width: 44px;
            height: 44px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            background: white;
            border: 2px solid #e2e8f0;
            flex-shrink: 0;
        }
        
        @media (min-width: 640px) {
            .benefit-icon { width: 52px; height: 52px; font-size: 1.25rem; }
        }
        
        .benefit-info {
            flex: 1;
            min-width: 0;
        }
        
        .benefit-name {
            font-size: 1rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.25rem;
            line-height: 1.3;
        }
        
        @media (min-width: 640px) {
            .benefit-name { font-size: 1.125rem; }
        }
        
        .benefit-category {
            font-size: 0.75rem;
            color: #64748b;
            font-weight: 500;
        }
        
        @media (min-width: 640px) {
            .benefit-category { font-size: 0.875rem; }
        }
        
        .status-indicator {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.625rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            flex-shrink: 0;
        }
        
        @media (min-width: 640px) {
            .status-indicator { padding: 0.375rem 0.75rem; font-size: 0.75rem; }
        }
        
        .status-verified { background: #dcfce7; color: #166534; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-disputed { background: #fecaca; color: #991b1b; }
        
        .card-body {
            padding: 1rem;
        }
        
        @media (min-width: 640px) {
            .card-body { padding: 1.5rem; }
        }
        
        .verification-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }
        
        @media (min-width: 640px) {
            .stat-value { font-size: 1.5rem; }
        }
        
        .stat-label {
            font-size: 0.625rem;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        @media (min-width: 640px) {
            .stat-label { font-size: 0.75rem; }
        }
        
        .progress-section {
            margin-bottom: 1rem;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            transition: width 0.3s ease;
        }
        
        .progress-text {
            font-size: 0.75rem;
            color: #64748b;
            text-align: center;
        }
        
        .card-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        @media (min-width: 640px) {
            .card-actions { flex-direction: row; }
        }
        
        .btn {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            touch-action: manipulation;
            min-height: 44px; /* Touch-friendly minimum */
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
            flex: 2;
        }
        
        .btn-primary:hover { background: #2563eb; }
        .btn-primary:active { background: #1d4ed8; transform: scale(0.98); }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
            flex: 1;
        }
        
        .btn-secondary:hover { background: #e2e8f0; }
        .btn-secondary:active { background: #cbd5e1; transform: scale(0.98); }
        
        .btn-danger {
            background: #ef4444;
            color: white;
            flex: 2;
        }
        
        .btn-danger:hover { background: #dc2626; }
        .btn-danger:active { background: #b91c1c; transform: scale(0.98); }
        
        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .benefit-card:hover { 
                transform: none; 
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); 
            }
            
            .btn:hover { 
                background: inherit; 
            }
        }
        
        /* Accessibility improvements */
        .btn:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }
        
        .benefit-card:focus-within {
            box-shadow: 0 0 0 2px #3b82f6;
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body { background: #0f172a; }
            .benefit-card { background: #1e293b; border-color: #334155; }
            .card-header { background: linear-gradient(135deg, #1e293b 0%, #334155 100%); }
            .benefit-name { color: #f1f5f9; }
            .benefit-category { color: #94a3b8; }
            .stat-item { background: #334155; border-color: #475569; }
            .stat-value { color: #f1f5f9; }
            .stat-label { color: #94a3b8; }
            .progress-text { color: #94a3b8; }
        }
    </style>
</head>
<body>
    <div class="card-container">
        <!-- Verified Benefit -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="benefit-main">
                    <div class="benefit-icon">🚴</div>
                    <div class="benefit-info">
                        <div class="benefit-name">Bike Leasing</div>
                        <div class="benefit-category">Transportation</div>
                    </div>
                    <div class="status-indicator status-verified">Verified</div>
                </div>
            </div>
            <div class="card-body">
                <div class="verification-stats">
                    <div class="stat-item">
                        <div class="stat-value">12</div>
                        <div class="stat-label">Confirmations</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0</div>
                        <div class="stat-label">Disputes</div>
                    </div>
                </div>
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    <div class="progress-text">Fully verified</div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-primary">✓ Confirmed</button>
                    <button class="btn btn-secondary">Info</button>
                </div>
            </div>
        </div>

        <!-- Pending Verification -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="benefit-main">
                    <div class="benefit-icon">🏋️</div>
                    <div class="benefit-info">
                        <div class="benefit-name">Gym Membership</div>
                        <div class="benefit-category">Health & Wellness</div>
                    </div>
                    <div class="status-indicator status-pending">Pending</div>
                </div>
            </div>
            <div class="card-body">
                <div class="verification-stats">
                    <div class="stat-item">
                        <div class="stat-value">2</div>
                        <div class="stat-label">Current</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">5</div>
                        <div class="stat-label">Needed</div>
                    </div>
                </div>
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 40%;"></div>
                    </div>
                    <div class="progress-text">3 more confirmations needed</div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-primary">Help Verify</button>
                    <button class="btn btn-secondary">Skip</button>
                </div>
            </div>
        </div>

        <!-- Disputed Benefit -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="benefit-main">
                    <div class="benefit-icon">🧘</div>
                    <div class="benefit-info">
                        <div class="benefit-name">Wellness Programs</div>
                        <div class="benefit-category">Mental Health</div>
                    </div>
                    <div class="status-indicator status-disputed">Disputed</div>
                </div>
            </div>
            <div class="card-body">
                <div class="verification-stats">
                    <div class="stat-item">
                        <div class="stat-value">1</div>
                        <div class="stat-label">Confirmations</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">2</div>
                        <div class="stat-label">Disputes</div>
                    </div>
                </div>
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 33%; background: linear-gradient(90deg, #ef4444, #dc2626);"></div>
                    </div>
                    <div class="progress-text">Needs resolution</div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-danger">Resolve</button>
                    <button class="btn btn-secondary">Details</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>