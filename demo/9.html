<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimalist Card Grid</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: #fafafa; 
            padding: 3rem 2rem; 
            line-height: 1.6;
        }
        
        .card-container { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 2rem; 
            max-width: 1000px; 
            margin: 0 auto; 
        }
        
        .benefit-card {
            background: white;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
            transition: all 0.2s ease;
            min-height: 200px;
            display: flex;
            flex-direction: column;
        }
        
        .benefit-card:hover { 
            border-color: #d0d0d0;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }
        
        .card-content {
            padding: 2rem;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .benefit-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .benefit-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            flex-shrink: 0;
        }
        
        .benefit-details {
            flex: 1;
            min-width: 0;
        }
        
        .benefit-name {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 0.25rem;
        }
        
        .benefit-category {
            font-size: 0.875rem;
            color: #666;
        }
        
        .verification-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .verification-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: #666;
        }
        
        .stat {
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }
        
        .stat-number {
            font-weight: 600;
            color: #1a1a1a;
        }
        
        .status-line {
            height: 2px;
            background: #f0f0f0;
            border-radius: 1px;
            margin-bottom: 0.75rem;
            overflow: hidden;
        }
        
        .status-fill {
            height: 100%;
            border-radius: 1px;
            transition: width 0.3s ease;
        }
        
        .status-verified { background: #22c55e; }
        .status-pending { background: #f59e0b; }
        .status-disputed { background: #ef4444; }
        
        .status-text {
            font-size: 0.75rem;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }
        
        .card-actions {
            margin-top: auto;
            padding-top: 1rem;
            border-top: 1px solid #f0f0f0;
            display: flex;
            gap: 0.75rem;
        }
        
        .action-btn {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid #e0e0e0;
            background: white;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .action-btn:hover {
            border-color: #ccc;
            background: #f9f9f9;
        }
        
        .action-primary {
            background: #1a1a1a;
            color: white;
            border-color: #1a1a1a;
        }
        
        .action-primary:hover {
            background: #333;
            border-color: #333;
        }
        
        .verified-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            color: #22c55e;
            font-weight: 500;
        }
        
        .pending-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            color: #f59e0b;
            font-weight: 500;
        }
        
        .disputed-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            color: #ef4444;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <!-- Verified Benefit -->
        <div class="benefit-card">
            <div class="card-content">
                <div class="benefit-header">
                    <div class="benefit-icon">🚴</div>
                    <div class="benefit-details">
                        <div class="benefit-name">Bike Leasing</div>
                        <div class="benefit-category">Transportation</div>
                    </div>
                </div>
                
                <div class="verification-info">
                    <div class="verification-stats">
                        <div class="stat">
                            <span>Confirmations:</span>
                            <span class="stat-number">12</span>
                        </div>
                        <div class="stat">
                            <span>Disputes:</span>
                            <span class="stat-number">0</span>
                        </div>
                    </div>
                    
                    <div class="status-line">
                        <div class="status-fill status-verified" style="width: 100%;"></div>
                    </div>
                    
                    <div class="verified-indicator">
                        <span>●</span>
                        <span>Verified by community</span>
                    </div>
                </div>
                
                <div class="card-actions">
                    <button class="action-btn action-primary">Confirmed</button>
                    <button class="action-btn">Details</button>
                </div>
            </div>
        </div>

        <!-- Pending Verification -->
        <div class="benefit-card">
            <div class="card-content">
                <div class="benefit-header">
                    <div class="benefit-icon">🏋️</div>
                    <div class="benefit-details">
                        <div class="benefit-name">Gym Membership</div>
                        <div class="benefit-category">Health & Wellness</div>
                    </div>
                </div>
                
                <div class="verification-info">
                    <div class="verification-stats">
                        <div class="stat">
                            <span>Confirmations:</span>
                            <span class="stat-number">3</span>
                        </div>
                        <div class="stat">
                            <span>Needed:</span>
                            <span class="stat-number">2 more</span>
                        </div>
                    </div>
                    
                    <div class="status-line">
                        <div class="status-fill status-pending" style="width: 60%;"></div>
                    </div>
                    
                    <div class="pending-indicator">
                        <span>●</span>
                        <span>Needs verification</span>
                    </div>
                </div>
                
                <div class="card-actions">
                    <button class="action-btn action-primary">Verify</button>
                    <button class="action-btn">Skip</button>
                </div>
            </div>
        </div>

        <!-- Disputed Benefit -->
        <div class="benefit-card">
            <div class="card-content">
                <div class="benefit-header">
                    <div class="benefit-icon">🧘</div>
                    <div class="benefit-details">
                        <div class="benefit-name">Wellness Programs</div>
                        <div class="benefit-category">Mental Health</div>
                    </div>
                </div>
                
                <div class="verification-info">
                    <div class="verification-stats">
                        <div class="stat">
                            <span>Confirmations:</span>
                            <span class="stat-number">1</span>
                        </div>
                        <div class="stat">
                            <span>Disputes:</span>
                            <span class="stat-number">2</span>
                        </div>
                    </div>
                    
                    <div class="status-line">
                        <div class="status-fill status-disputed" style="width: 33%;"></div>
                    </div>
                    
                    <div class="disputed-indicator">
                        <span>●</span>
                        <span>Under review</span>
                    </div>
                </div>
                
                <div class="card-actions">
                    <button class="action-btn action-primary">Resolve</button>
                    <button class="action-btn">View Issues</button>
                </div>
            </div>
        </div>

        <!-- New Benefit -->
        <div class="benefit-card">
            <div class="card-content">
                <div class="benefit-header">
                    <div class="benefit-icon">🍽️</div>
                    <div class="benefit-details">
                        <div class="benefit-name">Meal Vouchers</div>
                        <div class="benefit-category">Food & Dining</div>
                    </div>
                </div>
                
                <div class="verification-info">
                    <div class="verification-stats">
                        <div class="stat">
                            <span>Confirmations:</span>
                            <span class="stat-number">0</span>
                        </div>
                        <div class="stat">
                            <span>Status:</span>
                            <span class="stat-number">New</span>
                        </div>
                    </div>
                    
                    <div class="status-line">
                        <div class="status-fill" style="width: 0%; background: #e0e0e0;"></div>
                    </div>
                    
                    <div class="status-text">
                        Awaiting first verification
                    </div>
                </div>
                
                <div class="card-actions">
                    <button class="action-btn action-primary">Be First to Verify</button>
                    <button class="action-btn">Remove</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>