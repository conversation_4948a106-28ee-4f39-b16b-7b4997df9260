<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clean Minimal Cards</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; padding: 2rem; }
        
        .card-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 1.5rem; max-width: 1200px; margin: 0 auto; }
        
        .benefit-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
            position: relative;
            min-height: 180px;
            display: flex;
            flex-direction: column;
        }
        
        .benefit-card:hover { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transform: translateY(-2px); }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }
        
        .benefit-icon {
            width: 40px;
            height: 40px;
            background: #f1f5f9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }
        
        .benefit-name {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            flex: 1;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .status-verified { background: #10b981; }
        .status-pending { background: #f59e0b; }
        .status-unverified { background: #6b7280; }
        
        .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }
        
        .verification-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.875rem;
            color: #64748b;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .card-actions {
            margin-top: auto;
            padding-top: 1rem;
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary { background: #3b82f6; color: white; }
        .btn-primary:hover { background: #2563eb; }
        
        .btn-secondary { background: #f1f5f9; color: #475569; border: 1px solid #e2e8f0; }
        .btn-secondary:hover { background: #e2e8f0; }
        
        .verified-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: #dcfce7;
            color: #166534;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <!-- Verified Benefit -->
        <div class="benefit-card">
            <div class="verified-badge">✓ Verified</div>
            <div class="card-header">
                <div class="benefit-icon">🚴</div>
                <h3 class="benefit-name">Bike Leasing</h3>
                <div class="status-indicator status-verified"></div>
            </div>
            <div class="card-content">
                <div class="verification-stats">
                    <div class="stat-item">
                        <span>👥</span>
                        <span>12 confirmations</span>
                    </div>
                    <div class="stat-item">
                        <span>⚠️</span>
                        <span>0 disputes</span>
                    </div>
                </div>
            </div>
            <div class="card-actions">
                <button class="btn btn-primary">Confirm</button>
                <button class="btn btn-secondary">Dispute</button>
            </div>
        </div>

        <!-- Pending Benefit -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="benefit-icon">🏋️</div>
                <h3 class="benefit-name">Gym Membership</h3>
                <div class="status-indicator status-pending"></div>
            </div>
            <div class="card-content">
                <div class="verification-stats">
                    <div class="stat-item">
                        <span>👥</span>
                        <span>3 confirmations needed</span>
                    </div>
                </div>
                <p style="font-size: 0.875rem; color: #64748b;">Help verify this benefit!</p>
            </div>
            <div class="card-actions">
                <button class="btn btn-primary">Confirm Benefit</button>
            </div>
        </div>

        <!-- Unverified Benefit -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="benefit-icon">🏃</div>
                <h3 class="benefit-name">Sports Programs</h3>
                <div class="status-indicator status-unverified"></div>
            </div>
            <div class="card-content">
                <div class="verification-stats">
                    <div class="stat-item">
                        <span>👥</span>
                        <span>1 confirmation</span>
                    </div>
                </div>
            </div>
            <div class="card-actions">
                <button class="btn btn-primary">Confirm</button>
                <button class="btn btn-secondary">Remove</button>
            </div>
        </div>
    </div>
</body>
</html>