<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Proof & Community Cards</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; padding: 2rem; }
        
        .card-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem; max-width: 1200px; margin: 0 auto; }
        
        .benefit-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .benefit-card:hover { box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12); transform: translateY(-4px); }
        
        .card-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
        }
        
        .community-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .benefit-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }
        
        .benefit-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            backdrop-filter: blur(10px);
        }
        
        .benefit-name {
            font-size: 1.375rem;
            font-weight: 700;
        }
        
        .community-stats {
            display: flex;
            gap: 1.5rem;
            font-size: 0.875rem;
            opacity: 0.9;
        }
        
        .stat {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .social-proof {
            margin-bottom: 1.5rem;
        }
        
        .user-avatars {
            display: flex;
            margin-bottom: 0.75rem;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: 2px solid white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            color: white;
            font-weight: 600;
            margin-left: -8px;
        }
        
        .avatar:first-child { margin-left: 0; }
        
        .avatar-more {
            background: #f1f5f9;
            color: #64748b;
            font-size: 0.625rem;
        }
        
        .social-text {
            font-size: 0.875rem;
            color: #64748b;
            line-height: 1.5;
        }
        
        .verification-progress {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .progress-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        
        .progress-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
        }
        
        .progress-percentage {
            font-size: 0.875rem;
            font-weight: 700;
            color: #10b981;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            transition: width 0.3s ease;
        }
        
        .progress-description {
            font-size: 0.75rem;
            color: #64748b;
        }
        
        .recent-activity {
            margin-bottom: 1.5rem;
        }
        
        .activity-header {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
            font-size: 0.875rem;
            color: #64748b;
        }
        
        .activity-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.625rem;
            font-weight: 600;
        }
        
        .activity-time {
            margin-left: auto;
            font-size: 0.75rem;
            color: #9ca3af;
        }
        
        .action-section {
            border-top: 1px solid #f1f5f9;
            padding-top: 1rem;
        }
        
        .primary-action {
            width: 100%;
            padding: 0.875rem;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .action-verified { background: #dcfce7; color: #166534; }
        .action-verify { background: #3b82f6; color: white; }
        .action-resolve { background: #f59e0b; color: white; }
        
        .secondary-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .secondary-btn {
            flex: 1;
            padding: 0.5rem;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .secondary-btn:hover { background: #f8fafc; }
    </style>
</head>
<body>
    <div class="card-container">
        <!-- Verified with High Community Engagement -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="community-badge">🏆 Top Benefit</div>
                <div class="benefit-title">
                    <div class="benefit-icon">🚴</div>
                    <div class="benefit-name">Bike Leasing</div>
                </div>
                <div class="community-stats">
                    <div class="stat">
                        <span>👥</span>
                        <span>24 employees</span>
                    </div>
                    <div class="stat">
                        <span>⭐</span>
                        <span>4.8 rating</span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="social-proof">
                    <div class="user-avatars">
                        <div class="avatar">JS</div>
                        <div class="avatar">MK</div>
                        <div class="avatar">AL</div>
                        <div class="avatar">RG</div>
                        <div class="avatar avatar-more">+8</div>
                    </div>
                    <div class="social-text">
                        <strong>12 colleagues</strong> confirmed this benefit is available and highly recommend it.
                    </div>
                </div>
                
                <div class="verification-progress">
                    <div class="progress-header">
                        <div class="progress-title">Community Verification</div>
                        <div class="progress-percentage">100%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    <div class="progress-description">Fully verified by community consensus</div>
                </div>
                
                <div class="recent-activity">
                    <div class="activity-header">Recent Activity</div>
                    <div class="activity-item">
                        <div class="activity-avatar">MK</div>
                        <span>Maria confirmed and rated 5 stars</span>
                        <div class="activity-time">2h ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-avatar">JS</div>
                        <span>John shared usage tips</span>
                        <div class="activity-time">1d ago</div>
                    </div>
                </div>
                
                <div class="action-section">
                    <button class="primary-action action-verified">
                        <span>✓</span>
                        <span>You've Confirmed This</span>
                    </button>
                    <div class="secondary-actions">
                        <button class="secondary-btn">View Reviews</button>
                        <button class="secondary-btn">Share Tips</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Needs Community Input -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="community-badge">🤝 Help Needed</div>
                <div class="benefit-title">
                    <div class="benefit-icon">🏋️</div>
                    <div class="benefit-name">Gym Membership</div>
                </div>
                <div class="community-stats">
                    <div class="stat">
                        <span>👥</span>
                        <span>3 employees</span>
                    </div>
                    <div class="stat">
                        <span>❓</span>
                        <span>Unrated</span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="social-proof">
                    <div class="user-avatars">
                        <div class="avatar">AL</div>
                        <div class="avatar">RG</div>
                        <div class="avatar">TM</div>
                    </div>
                    <div class="social-text">
                        <strong>3 colleagues</strong> are interested in this benefit. Help verify if it's available!
                    </div>
                </div>
                
                <div class="verification-progress">
                    <div class="progress-header">
                        <div class="progress-title">Verification Progress</div>
                        <div class="progress-percentage">40%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 40%;"></div>
                    </div>
                    <div class="progress-description">2 confirmations, 3 more needed</div>
                </div>
                
                <div class="recent-activity">
                    <div class="activity-header">Recent Activity</div>
                    <div class="activity-item">
                        <div class="activity-avatar">AL</div>
                        <span>Alex asked for more details</span>
                        <div class="activity-time">3h ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-avatar">RG</div>
                        <span>Robert confirmed availability</span>
                        <div class="activity-time">1d ago</div>
                    </div>
                </div>
                
                <div class="action-section">
                    <button class="primary-action action-verify">
                        <span>🤝</span>
                        <span>Help Your Colleagues Verify</span>
                    </button>
                    <div class="secondary-actions">
                        <button class="secondary-btn">Ask Question</button>
                        <button class="secondary-btn">Skip</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Disputed - Community Resolution Needed -->
        <div class="benefit-card">
            <div class="card-header">
                <div class="community-badge">⚠️ Disputed</div>
                <div class="benefit-title">
                    <div class="benefit-icon">🧘</div>
                    <div class="benefit-name">Wellness Programs</div>
                </div>
                <div class="community-stats">
                    <div class="stat">
                        <span>👥</span>
                        <span>6 employees</span>
                    </div>
                    <div class="stat">
                        <span>⚖️</span>
                        <span>Mixed reviews</span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="social-proof">
                    <div class="user-avatars">
                        <div class="avatar">JS</div>
                        <div class="avatar">MK</div>
                        <div class="avatar">AL</div>
                        <div class="avatar avatar-more">+3</div>
                    </div>
                    <div class="social-text">
                        <strong>Mixed feedback</strong> from colleagues. Some confirm, others dispute availability.
                    </div>
                </div>
                
                <div class="verification-progress">
                    <div class="progress-header">
                        <div class="progress-title">Community Consensus</div>
                        <div class="progress-percentage">33%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 33%; background: linear-gradient(90deg, #f59e0b, #d97706);"></div>
                    </div>
                    <div class="progress-description">1 confirm, 2 disputes - needs resolution</div>
                </div>
                
                <div class="recent-activity">
                    <div class="activity-header">Recent Disputes</div>
                    <div class="activity-item">
                        <div class="activity-avatar">MK</div>
                        <span>Maria disputed: "Program discontinued"</span>
                        <div class="activity-time">1h ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-avatar">JS</div>
                        <span>John confirmed: "Still available"</span>
                        <div class="activity-time">2d ago</div>
                    </div>
                </div>
                
                <div class="action-section">
                    <button class="primary-action action-resolve">
                        <span>⚖️</span>
                        <span>Help Resolve Community Dispute</span>
                    </button>
                    <div class="secondary-actions">
                        <button class="secondary-btn">View All Feedback</button>
                        <button class="secondary-btn">Contact HR</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>