<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compact List-Style Cards</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; padding: 2rem; }
        
        .card-container { max-width: 800px; margin: 0 auto; }
        
        .benefits-list {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .list-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }
        
        .list-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .list-subtitle {
            font-size: 0.875rem;
            opacity: 0.9;
        }
        
        .benefit-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .benefit-item:last-child { border-bottom: none; }
        .benefit-item:hover { background: #f8fafc; }
        
        .benefit-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .benefit-content {
            flex: 1;
            min-width: 0;
        }
        
        .benefit-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }
        
        .benefit-name {
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        .status-badge {
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            font-size: 0.625rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .badge-verified { background: #dcfce7; color: #166534; }
        .badge-pending { background: #fef3c7; color: #92400e; }
        .badge-disputed { background: #fecaca; color: #991b1b; }
        
        .benefit-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.75rem;
            color: #64748b;
        }
        
        .stat {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .benefit-actions {
            display: flex;
            gap: 0.5rem;
            margin-left: 1rem;
        }
        
        .action-btn {
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-confirm { background: #10b981; color: white; }
        .btn-confirm:hover { background: #059669; }
        
        .btn-dispute { background: #ef4444; color: white; }
        .btn-dispute:hover { background: #dc2626; }
        
        .btn-verify { background: #3b82f6; color: white; }
        .btn-verify:hover { background: #2563eb; }
        
        .btn-neutral { background: #f1f5f9; color: #475569; border: 1px solid #e2e8f0; }
        .btn-neutral:hover { background: #e2e8f0; }
        
        .progress-indicator {
            width: 60px;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
            margin-left: 1rem;
        }
        
        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .progress-verified { background: #10b981; }
        .progress-pending { background: #f59e0b; }
        .progress-disputed { background: #ef4444; }
        
        .category-divider {
            background: #f8fafc;
            padding: 0.75rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .summary-stats {
            background: #f8fafc;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
        }
        
        .summary-label {
            font-size: 0.75rem;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .quick-actions {
            padding: 1rem 1.5rem;
            background: #f8fafc;
            display: flex;
            gap: 0.75rem;
            justify-content: center;
        }
        
        .quick-btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .quick-primary { background: #3b82f6; color: white; }
        .quick-primary:hover { background: #2563eb; }
        
        .quick-secondary { background: white; color: #475569; border: 1px solid #e2e8f0; }
        .quick-secondary:hover { background: #f8fafc; }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="benefits-list">
            <div class="list-header">
                <div class="list-title">Company Benefits</div>
                <div class="list-subtitle">Manage and verify your workplace benefits</div>
            </div>
            
            <div class="summary-stats">
                <div class="summary-item">
                    <div class="summary-value">8</div>
                    <div class="summary-label">Total Benefits</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">5</div>
                    <div class="summary-label">Verified</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">2</div>
                    <div class="summary-label">Pending</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">1</div>
                    <div class="summary-label">Disputed</div>
                </div>
            </div>
            
            <div class="category-divider">Health & Wellness</div>
            
            <div class="benefit-item">
                <div class="benefit-icon">🏋️</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Gym Membership</div>
                        <div class="status-badge badge-verified">Verified</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span>👥</span>
                            <span>12 confirmations</span>
                        </div>
                        <div class="stat">
                            <span>⭐</span>
                            <span>4.8 rating</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-verified" style="width: 100%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-confirm">✓ Confirmed</button>
                    <button class="action-btn btn-neutral">Info</button>
                </div>
            </div>
            
            <div class="benefit-item">
                <div class="benefit-icon">🧘</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Wellness Programs</div>
                        <div class="status-badge badge-disputed">Disputed</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span>👥</span>
                            <span>1 confirmation</span>
                        </div>
                        <div class="stat">
                            <span>⚠️</span>
                            <span>2 disputes</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-disputed" style="width: 33%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-verify">Resolve</button>
                    <button class="action-btn btn-neutral">Details</button>
                </div>
            </div>
            
            <div class="category-divider">Transportation</div>
            
            <div class="benefit-item">
                <div class="benefit-icon">🚴</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Bike Leasing Program</div>
                        <div class="status-badge badge-verified">Verified</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span>👥</span>
                            <span>18 confirmations</span>
                        </div>
                        <div class="stat">
                            <span>📈</span>
                            <span>Trending up</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-verified" style="width: 100%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-confirm">✓ Confirmed</button>
                    <button class="action-btn btn-neutral">Rate</button>
                </div>
            </div>
            
            <div class="benefit-item">
                <div class="benefit-icon">🚗</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Parking Allowance</div>
                        <div class="status-badge badge-pending">Pending</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span>👥</span>
                            <span>3 confirmations</span>
                        </div>
                        <div class="stat">
                            <span>🎯</span>
                            <span>2 more needed</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-pending" style="width: 60%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-verify">Verify</button>
                    <button class="action-btn btn-neutral">Skip</button>
                </div>
            </div>
            
            <div class="category-divider">Financial</div>
            
            <div class="benefit-item">
                <div class="benefit-icon">💰</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Performance Bonus</div>
                        <div class="status-badge badge-verified">Verified</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span>👥</span>
                            <span>25 confirmations</span>
                        </div>
                        <div class="stat">
                            <span>⭐</span>
                            <span>4.9 rating</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-verified" style="width: 100%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-confirm">✓ Confirmed</button>
                    <button class="action-btn btn-neutral">Details</button>
                </div>
            </div>
            
            <div class="benefit-item">
                <div class="benefit-icon">📈</div>
                <div class="benefit-content">
                    <div class="benefit-header">
                        <div class="benefit-name">Stock Options</div>
                        <div class="status-badge badge-pending">Pending</div>
                    </div>
                    <div class="benefit-stats">
                        <div class="stat">
                            <span>👥</span>
                            <span>1 confirmation</span>
                        </div>
                        <div class="stat">
                            <span>❓</span>
                            <span>Needs verification</span>
                        </div>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-fill progress-pending" style="width: 20%;"></div>
                </div>
                <div class="benefit-actions">
                    <button class="action-btn btn-verify">Verify</button>
                    <button class="action-btn btn-neutral">Info</button>
                </div>
            </div>
            
            <div class="quick-actions">
                <button class="quick-btn quick-primary">Add New Benefit</button>
                <button class="quick-btn quick-secondary">Export Report</button>
                <button class="quick-btn quick-secondary">View Analytics</button>
            </div>
        </div>
    </div>
</body>
</html>