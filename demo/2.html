<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Status-Driven Cards</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; padding: 2rem; }
        
        .card-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; max-width: 1200px; margin: 0 auto; }
        
        .benefit-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .benefit-card:hover { transform: translateY(-4px); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); }
        
        .card-status-bar {
            height: 4px;
            width: 100%;
        }
        
        .status-verified { background: linear-gradient(90deg, #10b981, #059669); }
        .status-pending { background: linear-gradient(90deg, #f59e0b, #d97706); }
        .status-unverified { background: linear-gradient(90deg, #6b7280, #4b5563); }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .status-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        
        .status-badge {
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .badge-verified { background: #dcfce7; color: #166534; }
        .badge-pending { background: #fef3c7; color: #92400e; }
        .badge-unverified { background: #f1f5f9; color: #475569; }
        
        .benefit-main {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .benefit-icon-large {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
        }
        
        .benefit-info h3 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }
        
        .benefit-category {
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }
        
        .verification-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .metric {
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
        }
        
        .metric-label {
            font-size: 0.75rem;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 0.25rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.75rem;
        }
        
        .btn {
            flex: 1;
            padding: 0.75rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }
        
        .btn-confirm { background: #10b981; color: white; }
        .btn-confirm:hover { background: #059669; }
        
        .btn-dispute { background: #ef4444; color: white; }
        .btn-dispute:hover { background: #dc2626; }
        
        .btn-neutral { background: #f1f5f9; color: #475569; border: 1px solid #e2e8f0; }
        .btn-neutral:hover { background: #e2e8f0; }
    </style>
</head>
<body>
    <div class="card-container">
        <!-- Verified Benefit -->
        <div class="benefit-card">
            <div class="card-status-bar status-verified"></div>
            <div class="card-body">
                <div class="status-header">
                    <div class="status-badge badge-verified">Verified</div>
                </div>
                <div class="benefit-main">
                    <div class="benefit-icon-large">🚴</div>
                    <div class="benefit-info">
                        <h3>Bike Leasing</h3>
                        <div class="benefit-category">Transportation</div>
                    </div>
                </div>
                <div class="verification-metrics">
                    <div class="metric">
                        <div class="metric-value">12</div>
                        <div class="metric-label">Confirmations</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">0</div>
                        <div class="metric-label">Disputes</div>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-confirm">Confirmed</button>
                    <button class="btn btn-dispute">Dispute</button>
                </div>
            </div>
        </div>

        <!-- Pending Benefit -->
        <div class="benefit-card">
            <div class="card-status-bar status-pending"></div>
            <div class="card-body">
                <div class="status-header">
                    <div class="status-badge badge-pending">Needs Verification</div>
                </div>
                <div class="benefit-main">
                    <div class="benefit-icon-large">🏋️</div>
                    <div class="benefit-info">
                        <h3>Gym Membership</h3>
                        <div class="benefit-category">Health & Wellness</div>
                    </div>
                </div>
                <div class="verification-metrics">
                    <div class="metric">
                        <div class="metric-value">2</div>
                        <div class="metric-label">Confirmations</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">3</div>
                        <div class="metric-label">Needed</div>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-confirm">Confirm Benefit</button>
                </div>
            </div>
        </div>

        <!-- Unverified Benefit -->
        <div class="benefit-card">
            <div class="card-status-bar status-unverified"></div>
            <div class="card-body">
                <div class="status-header">
                    <div class="status-badge badge-unverified">Unverified</div>
                </div>
                <div class="benefit-main">
                    <div class="benefit-icon-large">🧘</div>
                    <div class="benefit-info">
                        <h3>Wellness Programs</h3>
                        <div class="benefit-category">Health & Wellness</div>
                    </div>
                </div>
                <div class="verification-metrics">
                    <div class="metric">
                        <div class="metric-value">1</div>
                        <div class="metric-label">Confirmations</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">0</div>
                        <div class="metric-label">Disputes</div>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-confirm">Confirm</button>
                    <button class="btn btn-neutral">Remove</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>